/**
 * 微信浏览器视频播放修复脚本
 * WeChat Browser Video Playback Fix
 */

class WeChatVideoFix {
    constructor() {
        this.isWeChat = /micromessenger/i.test(navigator.userAgent);
        this.isIOS = /iphone|ipad|ipod/i.test(navigator.userAgent);
        this.isAndroid = /android/i.test(navigator.userAgent);
        
        if (this.isWeChat) {
            this.init();
        }
    }
    
    init() {
        console.log('微信浏览器视频播放修复初始化');
        
        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.setupWeChatVideoFix();
            });
        } else {
            this.setupWeChatVideoFix();
        }
    }
    
    setupWeChatVideoFix() {
        const video = document.getElementById('video-player');
        if (!video) {
            console.warn('未找到视频元素');
            return;
        }
        
        console.log('开始设置微信浏览器视频修复');
        
        // 设置微信浏览器特殊属性
        this.setWeChatAttributes(video);
        
        // 处理微信浏览器播放策略
        this.handleWeChatPlayback(video);
        
        // 添加微信浏览器提示
        this.addWeChatTip();
        
        // 监听微信浏览器特殊事件
        this.bindWeChatEvents(video);
    }
    
    /**
     * 设置微信浏览器特殊属性
     */
    setWeChatAttributes(video) {
        // X5内核特殊属性
        video.setAttribute('x5-video-player-type', 'h5');
        video.setAttribute('x5-video-player-fullscreen', 'true');
        video.setAttribute('x5-video-orientation', 'portraint');
        video.setAttribute('x5-playsinline', 'true');
        
        // 通用内联播放属性
        video.setAttribute('playsinline', 'true');
        video.setAttribute('webkit-playsinline', 'true');
        
        // 预加载设置
        video.preload = 'metadata';
        
        // 控制栏设置
        video.controls = true;
        
        // 样式设置
        video.style.width = '100%';
        video.style.height = 'auto';
        video.style.objectFit = 'contain';
        
        console.log('微信浏览器视频属性设置完成');
    }
    
    /**
     * 处理微信浏览器播放策略
     */
    handleWeChatPlayback(video) {
        // 微信浏览器播放策略
        const playVideo = () => {
            // 尝试静音播放
            video.muted = true;
            const playPromise = video.play();
            
            if (playPromise !== undefined) {
                playPromise.then(() => {
                    console.log('微信浏览器静音播放成功');
                    // 播放成功后尝试取消静音
                    setTimeout(() => {
                        video.muted = false;
                        console.log('取消静音');
                    }, 500);
                }).catch(error => {
                    console.log('微信浏览器播放失败:', error);
                    video.muted = false;
                    this.showPlayButton(video);
                });
            }
        };
        
        // 监听用户交互
        const userInteractionEvents = ['touchstart', 'touchend', 'click'];
        const handleUserInteraction = () => {
            playVideo();
            // 移除事件监听器
            userInteractionEvents.forEach(event => {
                document.removeEventListener(event, handleUserInteraction);
            });
        };
        
        // 添加用户交互监听器
        userInteractionEvents.forEach(event => {
            document.addEventListener(event, handleUserInteraction, { once: true });
        });
        
        // 微信浏览器特殊处理：监听WeixinJSBridge
        if (typeof WeixinJSBridge !== 'undefined') {
            WeixinJSBridge.invoke('getNetworkType', {}, () => {
                playVideo();
            });
        } else {
            document.addEventListener('WeixinJSBridgeReady', () => {
                WeixinJSBridge.invoke('getNetworkType', {}, () => {
                    playVideo();
                });
            });
        }
    }
    
    /**
     * 显示播放按钮
     */
    showPlayButton(video) {
        // 检查是否已存在播放按钮
        if (video.parentElement.querySelector('.wechat-play-button')) {
            return;
        }
        
        const playButton = document.createElement('div');
        playButton.className = 'wechat-play-button';
        playButton.innerHTML = `
            <div class="play-btn-wrapper">
                <i class="fas fa-play"></i>
                <span>点击播放视频</span>
            </div>
        `;
        
        // 样式设置
        playButton.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            cursor: pointer;
            z-index: 10;
            backdrop-filter: blur(2px);
        `;
        
        const playBtnWrapper = playButton.querySelector('.play-btn-wrapper');
        playBtnWrapper.style.cssText = `
            text-align: center;
            padding: 20px;
            border: 2px solid rgba(255, 255, 255, 0.8);
            border-radius: 50px;
            background: rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        `;
        
        const icon = playButton.querySelector('.fas');
        icon.style.cssText = `
            font-size: 2rem;
            margin-bottom: 0.5rem;
            display: block;
        `;
        
        const span = playButton.querySelector('span');
        span.style.cssText = `
            font-size: 0.9rem;
            display: block;
        `;
        
        // 点击事件
        playButton.addEventListener('click', () => {
            video.muted = false;
            video.play().then(() => {
                playButton.remove();
            }).catch(error => {
                console.error('播放失败:', error);
            });
        });
        
        // 添加到视频容器
        video.parentElement.style.position = 'relative';
        video.parentElement.appendChild(playButton);
    }
    
    /**
     * 添加微信浏览器提示
     */
    addWeChatTip() {
        // 检查是否已存在提示
        if (document.querySelector('.wechat-tip')) {
            return;
        }
        
        const tip = document.createElement('div');
        tip.className = 'alert alert-info wechat-tip mt-3';
        tip.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="fab fa-weixin me-2" style="font-size: 1.2rem; color: #07c160;"></i>
                <div class="flex-grow-1">
                    <strong>微信浏览器提示：</strong>
                    <br><small>如果视频无法播放，请点击右上角 "..." 选择 "在浏览器中打开"</small>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        // 添加到视频容器后面
        const videoContainer = document.querySelector('.video-player-container');
        if (videoContainer && videoContainer.nextElementSibling) {
            videoContainer.parentNode.insertBefore(tip, videoContainer.nextElementSibling);
        } else if (videoContainer) {
            videoContainer.parentNode.appendChild(tip);
        }
    }
    
    /**
     * 绑定微信浏览器特殊事件
     */
    bindWeChatEvents(video) {
        // 监听播放事件
        video.addEventListener('play', () => {
            console.log('微信浏览器视频开始播放');
            // 移除播放按钮
            const playButton = video.parentElement.querySelector('.wechat-play-button');
            if (playButton) {
                playButton.remove();
            }
        });
        
        // 监听暂停事件
        video.addEventListener('pause', () => {
            console.log('微信浏览器视频暂停');
        });
        
        // 监听错误事件
        video.addEventListener('error', (e) => {
            console.error('微信浏览器视频播放错误:', e);
            this.handleWeChatError(video, e);
        });
        
        // 监听页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible') {
                console.log('微信页面重新可见');
                // 可以在这里添加恢复播放的逻辑
            }
        });
    }
    
    /**
     * 处理微信浏览器错误
     */
    handleWeChatError(video, error) {
        let errorMessage = '视频播放出错';
        
        if (video.error) {
            switch (video.error.code) {
                case 1:
                    errorMessage = '视频加载被中止';
                    break;
                case 2:
                    errorMessage = '网络错误，请检查网络连接';
                    break;
                case 3:
                    errorMessage = '视频格式不支持，建议在浏览器中打开';
                    break;
                case 4:
                    errorMessage = '视频不存在或无法访问';
                    break;
            }
        }
        
        // 显示错误提示
        this.showWeChatError(errorMessage);
    }
    
    /**
     * 显示微信浏览器错误提示
     */
    showWeChatError(message) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'alert alert-warning mt-3';
        errorDiv.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <div class="flex-grow-1">
                    <strong>播放提示：</strong>${message}
                    <br><small class="text-muted">建议点击右上角"..."选择"在浏览器中打开"以获得更好的播放体验</small>
                </div>
                <button type="button" class="btn-close" onclick="this.parentElement.parentElement.remove()"></button>
            </div>
        `;
        
        const videoContainer = document.querySelector('.video-player-container');
        if (videoContainer) {
            videoContainer.appendChild(errorDiv);
        }
    }
}

// 立即初始化微信浏览器修复
new WeChatVideoFix();
