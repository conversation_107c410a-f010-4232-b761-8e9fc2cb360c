<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="color-scheme" content="light only">
    <meta name="theme-color" content="#ffffff">
    <meta name="msapplication-navbutton-color" content="#ffffff">
    <meta name="apple-mobile-web-app-status-bar-style" content="light-content">
    <meta name="supported-color-schemes" content="light">

    <!-- 微信浏览器优化 -->
    <meta name="format-detection" content="telephone=no">
    <meta name="x5-video-player-type" content="h5">
    <meta name="x5-video-player-fullscreen" content="true">
    <meta name="x5-video-orientation" content="portraint">
    <meta name="x5-playsinline" content="true">

    <!-- iOS Safari 优化 -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-touch-fullscreen" content="yes">

    <title th:text="${pageTitle}">视频播放</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">




    <!-- 自定义CSS -->
    <link href="/css/style.css" rel="stylesheet">
    <link href="/css/play-style.css" rel="stylesheet">




    </style>
</head>
<body style="background-color: #ffffff !important; color: #333333 !important; color-scheme: light only !important;">
    <!-- 导航栏 -->
    <nav class="navbar navbar-dark bg-primary sticky-top">
        <div class="container">
            <div class="d-flex w-100 align-items-center justify-content-between">
                <!-- 导航链接 -->
                <div class="d-flex align-items-center">
                    <ul class="navbar-nav d-flex flex-row mb-0">
                        <li class="nav-item">
                            <a class="nav-link px-2" href="/">
                                <i class="fas fa-home me-1"></i>首页
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link px-2" href="/videos">
                                <i class="fas fa-video me-1"></i>视频
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link px-2" href="/admin">
                                <i class="fas fa-cog me-1"></i>管理
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- 搜索框 -->
                <div class="search-container">
                    <form class="d-flex search-form" action="/search" method="get">
                        <input class="form-control me-2 search-input-mobile" type="search" name="keyword" placeholder="搜索...">
                        <button class="btn btn-outline-light btn-sm" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                        <button class="btn btn-outline-light btn-sm ms-1" type="button" onclick="toggleSearch()">
                            <i class="fas fa-times"></i>
                        </button>
                    </form>
                    <button class="btn btn-outline-light btn-sm search-toggle" onclick="toggleSearch()">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="container-fluid px-3 py-4">
        <div class="row justify-content-center">
            <!-- 视频播放区域 -->
            <div class="col-12 col-md-8 col-lg-6">
                <!-- 视频播放器容器 -->
                <div class="video-player-container mb-4">
                    <div class="video-wrapper">
                        <!-- 兼容性增强的视频播放器 -->
                        <video
                            id="video-player"
                            controls
                            preload="metadata"
                            playsinline
                            webkit-playsinline
                            x5-video-player-type="h5"
                            x5-video-player-fullscreen="true"
                            x5-video-orientation="portraint"
                            x5-playsinline="true"
                            muted="false"
                            th:data-video-url="${video.videoUrl}"
                            th:data-video-id="${video.id}"
                            th:poster="${video.thumbnailUrl}"
                            style="width: 100%; height: auto; object-fit: contain;">
                            <source th:src="${video.videoUrl}" type="video/mp4">
                            <source th:src="${video.videoUrl}" type="video/webm">
                            <source th:src="${video.videoUrl}" type="video/ogg">
                            您的浏览器不支持HTML5视频播放。请尝试使用其他浏览器或更新您的浏览器版本。
                        </video>
                        <!-- 加载状态 -->
                        <div class="video-loading" id="video-loading">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </div>








                    </div>
                </div>

                <!-- 视频信息 -->
                <div class="video-info bg-white rounded-3 shadow-sm p-4 mb-4">
                    <h1 class="video-title h4 mb-3" th:text="${video.title}">视频标题</h1>
                    <div class="video-stats">
                        <time class="video-date text-muted" th:text="${#temporals.format(video.createdTime, 'yyyy-MM-dd')}">2024-01-01</time>
                    </div>
                    <!-- 视频描述 -->
                    <div class="video-description mt-3" th:if="${video.description}">
                        <p class="text-muted mb-0" th:text="${video.description}">视频描述</p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <!-- <h5><i class="fas fa-play-circle me-2"></i>佳茵轻康</h5> -->
                    <p class="mb-0">轻康自然，享瘦生活。</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">
                        <small>© 2025 加盟合作. <a href="/about" class="text-light text-decoration-none">联系我们&nbsp;&nbsp;&nbsp;</a></small>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>


    <!-- 自定义JS -->
    <script src="/js/main.js"></script>
    <script src="/js/mobile-compatibility.js"></script>
    <script src="/js/wechat-video-fix.js"></script>
    <script src="/js/player.js"></script>


    <script th:inline="javascript">
        // 兼容性增强的视频播放器初始化
        document.addEventListener('DOMContentLoaded', function() {
            const videoElement = document.getElementById('video-player');
            const videoUrl = videoElement.dataset.videoUrl;
            const videoId = videoElement.dataset.videoId;
            const loadingElement = document.getElementById('video-loading');

            // 检测浏览器环境
            const userAgent = navigator.userAgent.toLowerCase();
            const isWeChat = userAgent.indexOf('micromessenger') !== -1;
            const isIOS = /iphone|ipad|ipod/.test(userAgent);
            const isAndroid = userAgent.indexOf('android') !== -1;
            const isMobile = isIOS || isAndroid;

            console.log('浏览器环境检测:', {
                isWeChat: isWeChat,
                isIOS: isIOS,
                isAndroid: isAndroid,
                isMobile: isMobile,
                userAgent: userAgent
            });

            // 设置视频源
            if (videoUrl) {
                videoElement.src = videoUrl;

                // 微信浏览器特殊处理
                if (isWeChat) {
                    // 强制设置播放属性
                    videoElement.setAttribute('webkit-playsinline', 'true');
                    videoElement.setAttribute('playsinline', 'true');
                    videoElement.setAttribute('x5-video-player-type', 'h5');
                    videoElement.setAttribute('x5-video-player-fullscreen', 'true');
                    videoElement.setAttribute('x5-playsinline', 'true');

                    // 微信环境下预加载设置
                    videoElement.preload = 'auto';
                }

                // iOS Safari 特殊处理
                if (isIOS) {
                    videoElement.setAttribute('webkit-playsinline', 'true');
                    videoElement.setAttribute('playsinline', 'true');
                }
            }

            // 隐藏加载状态
            function hideLoading() {
                if (loadingElement) {
                    loadingElement.style.display = 'none';
                }
            }

            // 显示加载状态
            function showLoading() {
                if (loadingElement) {
                    loadingElement.style.display = 'flex';
                }
            }

            // 初始化加载状态
            showLoading();

            // 兼容性播放处理
            function handleCompatiblePlay() {
                // 微信浏览器播放优化
                if (isWeChat) {
                    // 尝试自动播放（静音状态）
                    videoElement.muted = true;
                    const playPromise = videoElement.play();

                    if (playPromise !== undefined) {
                        playPromise.then(() => {
                            console.log('微信浏览器自动播放成功');
                            // 播放成功后取消静音
                            setTimeout(() => {
                                videoElement.muted = false;
                            }, 100);
                        }).catch(error => {
                            console.log('微信浏览器自动播放失败，需要用户交互:', error);
                            videoElement.muted = false;
                        });
                    }
                }

                // iOS Safari 播放优化
                if (isIOS) {
                    // iOS 需要用户手势触发播放
                    videoElement.addEventListener('touchstart', function() {
                        if (videoElement.paused) {
                            videoElement.play().catch(error => {
                                console.log('iOS播放失败:', error);
                            });
                        }
                    }, { once: true });
                }
            }

            // 初始化兼容性播放
            handleCompatiblePlay();

            // 视频加载开始
            videoElement.addEventListener('loadstart', function() {
                console.log('开始加载视频');
                showLoading();
            });

            // 视频元数据加载完成
            videoElement.addEventListener('loadedmetadata', function() {
                console.log('视频元数据加载完成');
                hideLoading();

                // 微信浏览器元数据加载后的处理
                if (isWeChat) {
                    // 确保视频尺寸正确
                    videoElement.style.width = '100%';
                    videoElement.style.height = 'auto';
                }
            });

            // 视频数据加载完成
            videoElement.addEventListener('loadeddata', function() {
                console.log('视频数据加载完成');
                hideLoading();
            });

            // 视频可以播放
            videoElement.addEventListener('canplay', function() {
                console.log('视频可以播放');
                hideLoading();
            });

            // 视频缓冲中
            videoElement.addEventListener('waiting', function() {
                console.log('视频缓冲中');
                showLoading();
            });

            // 视频播放中
            videoElement.addEventListener('playing', function() {
                console.log('视频正在播放');
                hideLoading();
            });

            // 增强的错误处理
            videoElement.addEventListener('error', function(e) {
                console.error('视频播放出错:', e);
                hideLoading();

                let errorMessage = '视频加载失败，请检查网络连接。';
                let retryButton = '';

                if (videoElement.error) {
                    console.error('错误代码:', videoElement.error.code);
                    console.error('错误信息:', videoElement.error.message);

                    switch (videoElement.error.code) {
                        case 1:
                            errorMessage = '视频加载被中止。';
                            break;
                        case 2:
                            errorMessage = '网络错误，无法加载视频。';
                            retryButton = '<button class="btn btn-primary btn-sm ms-2" onclick="retryVideoLoad()">重试</button>';
                            break;
                        case 3:
                            errorMessage = isWeChat ?
                                '视频格式不支持，请在浏览器中打开。' :
                                '视频解码失败或格式不支持。';
                            break;
                        case 4:
                            errorMessage = '视频不存在或无法访问。';
                            break;
                    }
                }

                // 微信浏览器特殊错误提示
                if (isWeChat && videoElement.error && videoElement.error.code === 3) {
                    errorMessage += '<br><small class="text-muted">建议点击右上角"..."选择"在浏览器中打开"</small>';
                }

                // 显示错误信息
                const errorDiv = document.createElement('div');
                errorDiv.className = 'alert alert-danger mt-3';
                errorDiv.innerHTML = `<i class="fas fa-exclamation-triangle me-2"></i>${errorMessage}${retryButton}`;
                videoElement.parentNode.appendChild(errorDiv);
            });

            // 播放开始事件
            videoElement.addEventListener('play', function() {
                console.log('视频开始播放，ID:', videoId);
                hideLoading();

                // 微信浏览器播放成功后的处理
                if (isWeChat) {
                    // 确保全屏播放设置
                    videoElement.setAttribute('x5-video-player-fullscreen', 'true');
                }
            });

            // 视频暂停事件
            videoElement.addEventListener('pause', function() {
                console.log('视频已暂停，ID:', videoId);
            });

            // 视频结束事件
            videoElement.addEventListener('ended', function() {
                console.log('视频播放结束，ID:', videoId);
            });

            // 全局重试函数
            window.retryVideoLoad = function() {
                console.log('重试加载视频');
                showLoading();

                // 移除错误提示
                const errorAlerts = videoElement.parentNode.querySelectorAll('.alert-danger');
                errorAlerts.forEach(alert => alert.remove());

                // 重新加载视频
                videoElement.load();
            };

            // 微信浏览器特殊处理：监听页面可见性变化
            if (isWeChat) {
                document.addEventListener('visibilitychange', function() {
                    if (document.visibilityState === 'visible' && videoElement.paused) {
                        console.log('页面重新可见，尝试恢复播放');
                        // 可以在这里添加恢复播放的逻辑
                    }
                });
            }

            console.log('兼容性增强的视频播放器已准备就绪');
        });














    </script>



    <!-- 浏览器兼容性检测和提示 -->
    <script>
        // 检测浏览器兼容性并显示提示
        function checkBrowserCompatibility() {
            const userAgent = navigator.userAgent.toLowerCase();
            const isWeChat = userAgent.indexOf('micromessenger') !== -1;
            const isQQ = userAgent.indexOf('qq/') !== -1;
            const isUC = userAgent.indexOf('ucbrowser') !== -1;
            const isOldIE = userAgent.indexOf('msie') !== -1;

            // 如果是微信浏览器，显示提示
            if (isWeChat) {
                const tipDiv = document.createElement('div');
                tipDiv.className = 'alert alert-info mt-3';
                tipDiv.innerHTML = `
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>微信浏览器提示：</strong>如果视频无法播放，请点击右上角"..."选择"在浏览器中打开"
                    <button type="button" class="btn-close float-end" data-bs-dismiss="alert"></button>
                `;

                const videoContainer = document.querySelector('.video-player-container');
                if (videoContainer) {
                    videoContainer.appendChild(tipDiv);
                }
            }

            // 如果是老版本IE，显示不支持提示
            if (isOldIE) {
                const warningDiv = document.createElement('div');
                warningDiv.className = 'alert alert-warning mt-3';
                warningDiv.innerHTML = `
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>浏览器兼容性警告：</strong>您的浏览器版本过低，可能无法正常播放视频。建议使用Chrome、Firefox、Safari或Edge浏览器。
                `;

                const videoContainer = document.querySelector('.video-player-container');
                if (videoContainer) {
                    videoContainer.appendChild(warningDiv);
                }
            }
        }

        // 页面加载完成后检测兼容性
        document.addEventListener('DOMContentLoaded', checkBrowserCompatibility);
    </script>
</body>
</html>

