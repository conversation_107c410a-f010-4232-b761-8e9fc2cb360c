/**
 * 视频播放页面专用样式 - 响应式设计
 * Video Play Page Styles - Responsive Design
 */

/* 兼容性增强的视频播放器样式 */
#video-player {
    width: 100%;
    height: auto;
    max-width: 100%;
    background-color: #000;
    border-radius: 8px;
    object-fit: contain;
    display: block;

    /* 移动端优化 */
    -webkit-playsinline: true;
    -webkit-appearance: none;

    /* 微信浏览器优化 */
    -webkit-transform: translateZ(0);
    transform: translateZ(0);

    /* 防止视频被选中 */
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;

    /* 触摸优化 */
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
}

/* 播放器容器 - 响应式 */
.video-player-container {
    position: relative;
    background: #000;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
    width: 100%;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    border: 1px solid #e9ecef;
}

/* 视频包装器 - 16:9 宽高比 */
.video-wrapper {
    position: relative;
    width: 100%;
    padding-bottom: 56.25%; /* 16:9 宽高比 */
    background: #000;
    border-radius: 8px;
    overflow: hidden;
}

.video-wrapper #video-player {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 8px;
}

/* 视频信息区域 */
.video-info {
    background: #fff;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin: 1px 0px !important;
}

/* 覆盖Bootstrap的mb-4类，确保margin设置生效 */
.video-info.bg-white.rounded-3.shadow-sm.p-4.mb-4 {
    margin: 1px 0px !important;
}

/* 视频标题 */
.video-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    text-align: center;
}

/* 特定选择器：确保video-title h3 mb-3的内容居中 */
.video-title.h3.mb-3 {
    text-align: center !important;
}


/* 全屏按钮 */
.fullscreen-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    border: none;
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    z-index: 1000;
    transition: background 0.3s ease;
}

.fullscreen-btn:hover {
    background: rgba(0, 0, 0, 0.9);
}

/* 响应式设计 - 移动端优化 */
@media (max-width: 768px) {
    .video-player-container {
        margin: 0 auto 1rem;
        border-radius: 8px;
        max-width: 100%;
    }

    .video-wrapper {
        padding-bottom: 56.25%; /* 保持16:9比例 */
    }

    .video-info {
        padding: 1rem;
        margin: 0.5rem 0;
        border-radius: 8px;
    }

    .video-title {
        font-size: 1.25rem;
        text-align: center;
    }

    .video-stats {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    .video-actions {
        width: 100%;
        text-align: center;
    }
}

/* 加载状态 */
.video-loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 8px;
    z-index: 10;
}

.video-loading .spinner-border {
    width: 3rem;
    height: 3rem;
    color: #007bff;
}

/* 错误状态 */
.video-error {
    text-align: center;
    padding: 2rem;
    background: #f8f9fa;
    border-radius: 8px;
    color: #6c757d;
}

.video-error i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #dc3545;
}


/* 平板设备优化 */
@media (max-width: 991.98px) {
    .video-player-container {
        max-width: 500px;
    }

    .video-wrapper {
        padding-bottom: 56.25%; /* 保持16:9比例 */
    }
}

@media (max-width: 576px) {
    /* 小屏幕设备完全响应式 */
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .video-player-container {
        margin: 0 0 1rem 0;
        border-radius: 8px;
        max-width: 100%;
    }

    .video-wrapper {
        padding-bottom: 56.25%; /* 保持16:9比例 */
    }

    .video-info {
        margin: 0.5rem 0;
        padding: 0.75rem;
    }

    .video-title {
        font-size: 1.1rem;
        margin-bottom: 0.5rem;
    }

    .video-description {
        font-size: 0.9rem;
    }
}

/* 主容器样式调整 */
.container.my-4 {
    margin-top: 10px !important;
}

/* 页脚样式调整 */
.bg-dark.text-light.py-4.mt-5 {
    margin-top: 5px !important;
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
    .video-info {
        background: #2d3748;
        color: #e2e8f0;
    }

    .video-title {
        color: #f7fafc;
    }


}



/* 视频描述样式 */
.video-description {
    border-top: 1px solid #e9ecef;
    padding-top: 1rem;
    margin-top: 1rem;
}

.video-description p {
    line-height: 1.6;
    color: #6c757d;
}

.video-wrapper {
    height: 380px!important;
    width: 100%!important;
}

/* 移动端和微信浏览器兼容性样式 */

/* 微信浏览器特殊样式 */
.wechat-browser #video-player {
    /* 强制内联播放 */
    -webkit-playsinline: true !important;
    playsinline: true !important;

    /* X5内核特殊属性 */
    x5-video-player-type: h5 !important;
    x5-video-player-fullscreen: true !important;
    x5-video-orientation: portraint !important;
    x5-playsinline: true !important;
}

/* iOS Safari 特殊样式 */
.ios-browser #video-player {
    -webkit-playsinline: true !important;
    playsinline: true !important;
    -webkit-appearance: none;
}

/* Android 浏览器特殊样式 */
.android-browser #video-player {
    /* 防止全屏播放 */
    -webkit-playsinline: true !important;
    playsinline: true !important;
}

/* 移动端通用优化 */
@media (max-width: 768px) {
    .video-player-container {
        margin: 0;
        border-radius: 0;
        box-shadow: none;
        border: none;
    }

    .video-wrapper {
        border-radius: 0;
        height: auto !important;
        padding-bottom: 56.25%; /* 保持16:9比例 */
    }

    #video-player {
        border-radius: 0;
        /* 移动端全宽显示 */
        width: 100vw;
        max-width: 100vw;
        margin-left: calc(-50vw + 50%);
    }

    /* 移动端控制栏优化 */
    #video-player::-webkit-media-controls-panel {
        background-color: rgba(0, 0, 0, 0.8);
    }

    #video-player::-webkit-media-controls-play-button,
    #video-player::-webkit-media-controls-fullscreen-button {
        background-color: rgba(255, 255, 255, 0.9);
        border-radius: 50%;
    }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
    .video-info {
        padding: 1rem;
        margin: 0.5rem;
        border-radius: 8px;
    }

    .video-title {
        font-size: 1.1rem;
        line-height: 1.4;
    }

    .video-stats {
        font-size: 0.85rem;
    }
}

/* 横屏模式优化 */
@media (orientation: landscape) and (max-height: 500px) {
    .video-wrapper {
        height: 70vh !important;
        padding-bottom: 0 !important;
    }

    #video-player {
        height: 70vh;
        object-fit: contain;
    }
}

/* 微信浏览器检测和提示样式 */
.browser-tip {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    line-height: 1.4;
}

.browser-tip .fas {
    margin-right: 0.5rem;
    opacity: 0.9;
}

.browser-tip .btn-close {
    filter: invert(1);
    opacity: 0.8;
}

.browser-tip .btn-close:hover {
    opacity: 1;
}

/* 加载状态优化 */
.video-loading {
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
}

/* 错误提示优化 */
.alert-danger {
    border-left: 4px solid #dc3545;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.alert-info {
    border-left: 4px solid #0dcaf0;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

/* 防止页面缩放影响视频播放 */
@media (max-width: 768px) {
    body {
        -webkit-text-size-adjust: 100%;
        -ms-text-size-adjust: 100%;
    }

    /* 防止双击缩放 */
    * {
        -webkit-touch-callout: none;
        -webkit-tap-highlight-color: transparent;
    }

    /* 保持视频播放器可交互 */
    #video-player {
        -webkit-touch-callout: default;
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    }
}