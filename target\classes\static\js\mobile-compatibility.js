/**
 * 移动端浏览器兼容性检测和处理
 * Mobile Browser Compatibility Detection and Handling
 */

class MobileCompatibility {
    constructor() {
        this.userAgent = navigator.userAgent.toLowerCase();
        this.init();
    }
    
    init() {
        this.detectBrowser();
        this.addBrowserClasses();
        this.setupEventListeners();
        this.optimizeForMobile();
    }
    
    /**
     * 检测浏览器类型
     */
    detectBrowser() {
        this.browser = {
            isWeChat: this.userAgent.indexOf('micromessenger') !== -1,
            isQQ: this.userAgent.indexOf('qq/') !== -1,
            isUC: this.userAgent.indexOf('ucbrowser') !== -1,
            isIOS: /iphone|ipad|ipod/.test(this.userAgent),
            isAndroid: this.userAgent.indexOf('android') !== -1,
            isSafari: this.userAgent.indexOf('safari') !== -1 && this.userAgent.indexOf('chrome') === -1,
            isChrome: this.userAgent.indexOf('chrome') !== -1,
            isFirefox: this.userAgent.indexOf('firefox') !== -1,
            isEdge: this.userAgent.indexOf('edge') !== -1,
            isMobile: /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(this.userAgent)
        };
        
        console.log('浏览器检测结果:', this.browser);
    }
    
    /**
     * 为body添加浏览器相关的CSS类
     */
    addBrowserClasses() {
        const body = document.body;
        
        if (this.browser.isWeChat) body.classList.add('wechat-browser');
        if (this.browser.isQQ) body.classList.add('qq-browser');
        if (this.browser.isUC) body.classList.add('uc-browser');
        if (this.browser.isIOS) body.classList.add('ios-browser');
        if (this.browser.isAndroid) body.classList.add('android-browser');
        if (this.browser.isSafari) body.classList.add('safari-browser');
        if (this.browser.isChrome) body.classList.add('chrome-browser');
        if (this.browser.isMobile) body.classList.add('mobile-browser');
    }
    
    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 监听设备方向变化
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                this.handleOrientationChange();
            }, 100);
        });
        
        // 监听页面可见性变化
        document.addEventListener('visibilitychange', () => {
            this.handleVisibilityChange();
        });
        
        // 监听网络状态变化
        if ('onLine' in navigator) {
            window.addEventListener('online', () => {
                this.handleNetworkChange(true);
            });
            
            window.addEventListener('offline', () => {
                this.handleNetworkChange(false);
            });
        }
    }
    
    /**
     * 移动端优化
     */
    optimizeForMobile() {
        if (!this.browser.isMobile) return;
        
        // 防止页面缩放
        this.preventZoom();
        
        // 优化触摸体验
        this.optimizeTouch();
        
        // 处理软键盘
        this.handleSoftKeyboard();
    }
    
    /**
     * 防止页面缩放
     */
    preventZoom() {
        // 防止双击缩放
        let lastTouchEnd = 0;
        document.addEventListener('touchend', function(event) {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                event.preventDefault();
            }
            lastTouchEnd = now;
        }, false);
        
        // 防止手势缩放
        document.addEventListener('gesturestart', function(event) {
            event.preventDefault();
        });
    }
    
    /**
     * 优化触摸体验
     */
    optimizeTouch() {
        // 移除点击延迟
        document.addEventListener('touchstart', function() {}, true);
        
        // 优化滚动性能
        document.addEventListener('touchmove', function(event) {
            // 允许视频播放器的触摸事件
            if (event.target.tagName === 'VIDEO') {
                return;
            }
        }, { passive: true });
    }
    
    /**
     * 处理软键盘
     */
    handleSoftKeyboard() {
        const viewport = document.querySelector('meta[name=viewport]');
        if (viewport) {
            // 软键盘弹出时调整视口
            window.addEventListener('resize', () => {
                if (document.activeElement.tagName === 'INPUT' || 
                    document.activeElement.tagName === 'TEXTAREA') {
                    viewport.setAttribute('content', 
                        'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
                }
            });
        }
    }
    
    /**
     * 处理设备方向变化
     */
    handleOrientationChange() {
        console.log('设备方向改变');
        
        // 重新调整视频播放器尺寸
        const videoPlayer = document.getElementById('video-player');
        if (videoPlayer) {
            // 强制重新计算尺寸
            videoPlayer.style.width = '100%';
            videoPlayer.style.height = 'auto';
        }
        
        // 触发窗口resize事件
        window.dispatchEvent(new Event('resize'));
    }
    
    /**
     * 处理页面可见性变化
     */
    handleVisibilityChange() {
        const videoPlayer = document.getElementById('video-player');
        if (!videoPlayer) return;
        
        if (document.visibilityState === 'hidden') {
            console.log('页面隐藏，暂停视频');
            if (!videoPlayer.paused) {
                videoPlayer.pause();
                videoPlayer.dataset.wasPlaying = 'true';
            }
        } else if (document.visibilityState === 'visible') {
            console.log('页面显示');
            // 微信浏览器特殊处理
            if (this.browser.isWeChat && videoPlayer.dataset.wasPlaying === 'true') {
                console.log('尝试恢复播放');
                delete videoPlayer.dataset.wasPlaying;
                // 不自动恢复播放，让用户手动控制
            }
        }
    }
    
    /**
     * 处理网络状态变化
     */
    handleNetworkChange(isOnline) {
        const videoPlayer = document.getElementById('video-player');
        if (!videoPlayer) return;
        
        if (isOnline) {
            console.log('网络已连接');
            this.showNetworkStatus('网络已连接', 'success');
        } else {
            console.log('网络已断开');
            this.showNetworkStatus('网络连接已断开，视频可能无法播放', 'warning');
            if (!videoPlayer.paused) {
                videoPlayer.pause();
            }
        }
    }
    
    /**
     * 显示网络状态提示
     */
    showNetworkStatus(message, type) {
        // 移除之前的提示
        const existingAlert = document.querySelector('.network-status-alert');
        if (existingAlert) {
            existingAlert.remove();
        }
        
        // 创建新提示
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} network-status-alert`;
        alert.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 9999;
            min-width: 300px;
            text-align: center;
        `;
        alert.innerHTML = `
            <i class="fas fa-wifi me-2"></i>${message}
            <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
        `;
        
        document.body.appendChild(alert);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (alert.parentElement) {
                alert.remove();
            }
        }, 3000);
    }
    
    /**
     * 获取浏览器信息
     */
    getBrowserInfo() {
        return this.browser;
    }
    
    /**
     * 检查是否支持视频播放
     */
    checkVideoSupport() {
        const video = document.createElement('video');
        return {
            mp4: video.canPlayType('video/mp4') !== '',
            webm: video.canPlayType('video/webm') !== '',
            ogg: video.canPlayType('video/ogg') !== '',
            hls: video.canPlayType('application/vnd.apple.mpegurl') !== ''
        };
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    window.mobileCompatibility = new MobileCompatibility();
    
    // 输出浏览器支持信息
    console.log('视频格式支持:', window.mobileCompatibility.checkVideoSupport());
});
