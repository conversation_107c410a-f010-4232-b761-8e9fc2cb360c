/**
 * 兼容性增强的视频播放器
 * 支持微信、QQ、UC等移动端浏览器
 */

/**
 * 浏览器环境检测
 */
const BrowserDetector = {
    userAgent: navigator.userAgent.toLowerCase(),

    get isWeChat() {
        return this.userAgent.indexOf('micromessenger') !== -1;
    },

    get isQQ() {
        return this.userAgent.indexOf('qq/') !== -1;
    },

    get isUC() {
        return this.userAgent.indexOf('ucbrowser') !== -1;
    },

    get isIOS() {
        return /iphone|ipad|ipod/.test(this.userAgent);
    },

    get isAndroid() {
        return this.userAgent.indexOf('android') !== -1;
    },

    get isMobile() {
        return this.isIOS || this.isAndroid;
    },

    get isSafari() {
        return this.userAgent.indexOf('safari') !== -1 && this.userAgent.indexOf('chrome') === -1;
    },

    get isChrome() {
        return this.userAgent.indexOf('chrome') !== -1;
    }
};

/**
 * 兼容性视频播放器类
 */
class CompatibleVideoPlayer {
    constructor(videoElement) {
        this.video = videoElement;
        this.videoUrl = videoElement.dataset.videoUrl;
        this.videoId = videoElement.dataset.videoId;
        this.loadingElement = document.getElementById('video-loading');

        this.init();
    }

    init() {
        console.log('初始化兼容性视频播放器', {
            browser: BrowserDetector,
            videoUrl: this.videoUrl
        });

        this.setupVideoAttributes();
        this.bindEvents();
        this.handleBrowserSpecific();
    }

    /**
     * 设置视频属性以提高兼容性
     */
    setupVideoAttributes() {
        // 基础属性
        this.video.setAttribute('playsinline', 'true');
        this.video.setAttribute('webkit-playsinline', 'true');

        // 微信浏览器特殊属性
        if (BrowserDetector.isWeChat) {
            this.video.setAttribute('x5-video-player-type', 'h5');
            this.video.setAttribute('x5-video-player-fullscreen', 'true');
            this.video.setAttribute('x5-video-orientation', 'portraint');
            this.video.setAttribute('x5-playsinline', 'true');
            this.video.preload = 'auto';
        }

        // iOS Safari 特殊处理
        if (BrowserDetector.isIOS) {
            this.video.setAttribute('webkit-playsinline', 'true');
            this.video.setAttribute('playsinline', 'true');
        }

        // 设置视频源
        if (this.videoUrl) {
            this.video.src = this.videoUrl;
        }
    }



    /**
     * 绑定视频事件
     */
    bindEvents() {
        // 加载事件
        this.video.addEventListener('loadstart', () => {
            console.log('开始加载视频');
            this.showLoading();
        });

        this.video.addEventListener('loadedmetadata', () => {
            console.log('视频元数据加载完成');
            this.hideLoading();
            this.handleMetadataLoaded();
        });

        this.video.addEventListener('loadeddata', () => {
            console.log('视频数据加载完成');
            this.hideLoading();
        });

        this.video.addEventListener('canplay', () => {
            console.log('视频可以播放');
            this.hideLoading();
        });

        this.video.addEventListener('waiting', () => {
            console.log('视频缓冲中');
            this.showLoading();
        });

        this.video.addEventListener('playing', () => {
            console.log('视频正在播放');
            this.hideLoading();
        });

        // 播放控制事件
        this.video.addEventListener('play', () => {
            console.log('视频开始播放');
            this.hideLoading();
            this.handlePlayStart();
        });

        this.video.addEventListener('pause', () => {
            console.log('视频暂停');
        });

        this.video.addEventListener('ended', () => {
            console.log('视频播放结束');
        });

        // 错误处理
        this.video.addEventListener('error', (e) => {
            console.error('视频播放出错:', e);
            this.hideLoading();
            this.handleError();
        });
    }

    /**
     * 处理浏览器特定逻辑
     */
    handleBrowserSpecific() {
        if (BrowserDetector.isWeChat) {
            this.handleWeChatBrowser();
        }

        if (BrowserDetector.isIOS) {
            this.handleIOSBrowser();
        }

        if (BrowserDetector.isAndroid) {
            this.handleAndroidBrowser();
        }
    }

    /**
     * 微信浏览器特殊处理
     */
    handleWeChatBrowser() {
        console.log('微信浏览器特殊处理');

        // 尝试自动播放（静音状态）
        this.video.muted = true;
        const playPromise = this.video.play();

        if (playPromise !== undefined) {
            playPromise.then(() => {
                console.log('微信浏览器自动播放成功');
                // 播放成功后取消静音
                setTimeout(() => {
                    this.video.muted = false;
                }, 100);
            }).catch(error => {
                console.log('微信浏览器自动播放失败，需要用户交互:', error);
                this.video.muted = false;
            });
        }

        // 监听页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible' && this.video.paused) {
                console.log('页面重新可见，尝试恢复播放');
            }
        });
    }

    /**
     * iOS浏览器特殊处理
     */
    handleIOSBrowser() {
        console.log('iOS浏览器特殊处理');

        // iOS 需要用户手势触发播放
        this.video.addEventListener('touchstart', () => {
            if (this.video.paused) {
                this.video.play().catch(error => {
                    console.log('iOS播放失败:', error);
                });
            }
        }, { once: true });
    }

    /**
     * Android浏览器特殊处理
     */
    handleAndroidBrowser() {
        console.log('Android浏览器特殊处理');

        // Android Chrome 自动播放策略
        if (BrowserDetector.isChrome) {
            this.video.muted = true;
            this.video.play().then(() => {
                console.log('Android Chrome自动播放成功');
                this.video.muted = false;
            }).catch(error => {
                console.log('Android Chrome自动播放失败:', error);
                this.video.muted = false;
            });
        }
    }

    /**
     * 元数据加载完成处理
     */
    handleMetadataLoaded() {
        // 微信浏览器元数据加载后的处理
        if (BrowserDetector.isWeChat) {
            // 确保视频尺寸正确
            this.video.style.width = '100%';
            this.video.style.height = 'auto';
        }
    }

    /**
     * 播放开始处理
     */
    handlePlayStart() {
        // 微信浏览器播放成功后的处理
        if (BrowserDetector.isWeChat) {
            // 确保全屏播放设置
            this.video.setAttribute('x5-video-player-fullscreen', 'true');
        }
    }

    /**
     * 错误处理
     */
    handleError() {
        let errorMessage = '视频加载失败，请检查网络连接。';
        let retryButton = '';

        if (this.video.error) {
            console.error('错误代码:', this.video.error.code);
            console.error('错误信息:', this.video.error.message);

            switch (this.video.error.code) {
                case 1:
                    errorMessage = '视频加载被中止。';
                    break;
                case 2:
                    errorMessage = '网络错误，无法加载视频。';
                    retryButton = '<button class="btn btn-primary btn-sm ms-2" onclick="window.videoPlayer.retry()">重试</button>';
                    break;
                case 3:
                    errorMessage = BrowserDetector.isWeChat ?
                        '视频格式不支持，请在浏览器中打开。' :
                        '视频解码失败或格式不支持。';
                    break;
                case 4:
                    errorMessage = '视频不存在或无法访问。';
                    break;
            }
        }

        // 微信浏览器特殊错误提示
        if (BrowserDetector.isWeChat && this.video.error && this.video.error.code === 3) {
            errorMessage += '<br><small class="text-muted">建议点击右上角"..."选择"在浏览器中打开"</small>';
        }

        // 显示错误信息
        this.showError(errorMessage + retryButton);
    }

    /**
     * 显示错误信息
     */
    showError(message) {
        // 移除之前的错误提示
        const existingErrors = this.video.parentNode.querySelectorAll('.alert-danger');
        existingErrors.forEach(error => error.remove());

        const errorDiv = document.createElement('div');
        errorDiv.className = 'alert alert-danger mt-3';
        errorDiv.innerHTML = `<i class="fas fa-exclamation-triangle me-2"></i>${message}`;
        this.video.parentNode.appendChild(errorDiv);
    }

    /**
     * 显示加载状态
     */
    showLoading() {
        if (this.loadingElement) {
            this.loadingElement.style.display = 'flex';
        }
    }

    /**
     * 隐藏加载状态
     */
    hideLoading() {
        if (this.loadingElement) {
            this.loadingElement.style.display = 'none';
        }
    }

    /**
     * 重试加载视频
     */
    retry() {
        console.log('重试加载视频');
        this.showLoading();

        // 移除错误提示
        const errorAlerts = this.video.parentNode.querySelectorAll('.alert-danger');
        errorAlerts.forEach(alert => alert.remove());

        // 重新加载视频
        this.video.load();
    }

    /**
     * 销毁播放器
     */
    destroy() {
        // 移除事件监听器
        this.video.removeEventListener('loadstart', this.showLoading);
        this.video.removeEventListener('error', this.handleError);
        // ... 其他事件监听器

        console.log('视频播放器已销毁');
    }
}

/**
 * 初始化播放器
 */
function initializeVideoPlayer() {
    const videoElement = document.getElementById('video-player');
    if (!videoElement) {
        console.warn('未找到视频元素');
        return;
    }

    // 创建兼容性播放器实例
    window.videoPlayer = new CompatibleVideoPlayer(videoElement);
    console.log('兼容性视频播放器初始化完成');
}

/**
 * 页面加载完成时初始化
 */
document.addEventListener('DOMContentLoaded', initializeVideoPlayer);

/**
 * 页面卸载时清理
 */
window.addEventListener('beforeunload', function() {
    if (window.videoPlayer) {
        window.videoPlayer.destroy();
    }
});
