<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="color-scheme" content="light only">
    <meta name="theme-color" content="#ffffff">
    
    <!-- 微信浏览器优化 -->
    <meta name="format-detection" content="telephone=no">
    <meta name="x5-video-player-type" content="h5">
    <meta name="x5-video-player-fullscreen" content="true">
    <meta name="x5-video-orientation" content="portraint">
    <meta name="x5-playsinline" content="true">
    
    <!-- iOS Safari 优化 -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-touch-fullscreen" content="yes">
    
    <title>视频播放器兼容性测试</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- 自定义CSS -->
    <link href="/css/style.css" rel="stylesheet">
    <link href="/css/play-style.css" rel="stylesheet">
</head>
<body style="background-color: #ffffff !important; color: #333333 !important;">
    <!-- 导航栏 -->
    <nav class="navbar navbar-dark bg-primary">
        <div class="container">
            <span class="navbar-brand">
                <i class="fas fa-play-circle me-2"></i>视频播放器兼容性测试
            </span>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="container-fluid px-3 py-4">
        <div class="row justify-content-center">
            <div class="col-12 col-md-8 col-lg-6">
                <!-- 浏览器信息 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>浏览器信息</h5>
                    </div>
                    <div class="card-body">
                        <div id="browser-info">
                            <p><strong>User Agent:</strong> <span id="user-agent"></span></p>
                            <p><strong>浏览器类型:</strong> <span id="browser-type"></span></p>
                            <p><strong>设备类型:</strong> <span id="device-type"></span></p>
                            <p><strong>视频支持:</strong> <span id="video-support"></span></p>
                        </div>
                    </div>
                </div>

                <!-- 视频播放器容器 -->
                <div class="video-player-container mb-4">
                    <div class="video-wrapper">
                        <!-- 兼容性增强的视频播放器 -->
                        <video
                            id="video-player"
                            controls
                            preload="metadata"
                            playsinline
                            webkit-playsinline
                            x5-video-player-type="h5"
                            x5-video-player-fullscreen="true"
                            x5-video-orientation="portraint"
                            x5-playsinline="true"
                            muted="false"
                            data-video-url="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4"
                            data-video-id="test"
                            style="width: 100%; height: auto; object-fit: contain;">
                            <source src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4" type="video/mp4">
                            <source src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4" type="video/webm">
                            您的浏览器不支持HTML5视频播放。请尝试使用其他浏览器或更新您的浏览器版本。
                        </video>
                        
                        <!-- 加载状态 -->
                        <div class="video-loading" id="video-loading">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 测试控制 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>测试控制</h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-2">
                            <div class="col-6 col-md-3">
                                <button class="btn btn-primary btn-sm w-100" onclick="testPlay()">
                                    <i class="fas fa-play me-1"></i>播放
                                </button>
                            </div>
                            <div class="col-6 col-md-3">
                                <button class="btn btn-secondary btn-sm w-100" onclick="testPause()">
                                    <i class="fas fa-pause me-1"></i>暂停
                                </button>
                            </div>
                            <div class="col-6 col-md-3">
                                <button class="btn btn-info btn-sm w-100" onclick="testMute()">
                                    <i class="fas fa-volume-mute me-1"></i>静音
                                </button>
                            </div>
                            <div class="col-6 col-md-3">
                                <button class="btn btn-warning btn-sm w-100" onclick="testReload()">
                                    <i class="fas fa-redo me-1"></i>重载
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 日志输出 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-terminal me-2"></i>测试日志</h5>
                        <button class="btn btn-sm btn-outline-secondary float-end" onclick="clearLog()">清空</button>
                    </div>
                    <div class="card-body">
                        <div id="test-log" style="height: 200px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 0.85rem;">
                            <!-- 日志内容 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 自定义JS -->
    <script src="/js/mobile-compatibility.js"></script>
    <script src="/js/wechat-video-fix.js"></script>
    <script src="/js/player.js"></script>

    <script>
        // 测试脚本
        let logContainer;
        let video;

        function log(message) {
            if (!logContainer) {
                logContainer = document.getElementById('test-log');
            }
            const time = new Date().toLocaleTimeString();
            logContainer.innerHTML += `[${time}] ${message}<br>`;
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function clearLog() {
            if (logContainer) {
                logContainer.innerHTML = '';
            }
        }

        function testPlay() {
            if (video) {
                video.play().then(() => {
                    log('✅ 播放成功');
                }).catch(error => {
                    log('❌ 播放失败: ' + error.message);
                });
            }
        }

        function testPause() {
            if (video) {
                video.pause();
                log('⏸️ 视频已暂停');
            }
        }

        function testMute() {
            if (video) {
                video.muted = !video.muted;
                log(video.muted ? '🔇 已静音' : '🔊 已取消静音');
            }
        }

        function testReload() {
            if (video) {
                video.load();
                log('🔄 视频重新加载');
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            video = document.getElementById('video-player');
            logContainer = document.getElementById('test-log');
            
            // 显示浏览器信息
            document.getElementById('user-agent').textContent = navigator.userAgent;
            
            // 检测浏览器类型
            const userAgent = navigator.userAgent.toLowerCase();
            let browserType = [];
            if (userAgent.indexOf('micromessenger') !== -1) browserType.push('微信');
            if (userAgent.indexOf('qq/') !== -1) browserType.push('QQ');
            if (userAgent.indexOf('ucbrowser') !== -1) browserType.push('UC');
            if (/iphone|ipad|ipod/.test(userAgent)) browserType.push('iOS');
            if (userAgent.indexOf('android') !== -1) browserType.push('Android');
            if (userAgent.indexOf('safari') !== -1 && userAgent.indexOf('chrome') === -1) browserType.push('Safari');
            if (userAgent.indexOf('chrome') !== -1) browserType.push('Chrome');
            
            document.getElementById('browser-type').textContent = browserType.join(', ') || '未知';
            document.getElementById('device-type').textContent = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent) ? '移动设备' : '桌面设备';
            
            // 检测视频支持
            const testVideo = document.createElement('video');
            const support = [];
            if (testVideo.canPlayType('video/mp4')) support.push('MP4');
            if (testVideo.canPlayType('video/webm')) support.push('WebM');
            if (testVideo.canPlayType('video/ogg')) support.push('OGG');
            document.getElementById('video-support').textContent = support.join(', ') || '无';
            
            log('🚀 测试页面初始化完成');
            log('📱 浏览器: ' + (browserType.join(', ') || '未知'));
            log('🎥 视频支持: ' + (support.join(', ') || '无'));
        });
    </script>
</body>
</html>
